import { App, Component } from 'vue'

export interface FormDesignerUtils {
  copyTextToClipboard: (text: string) => Promise<void>
  getInjectArg: (inject: any, name: string, defaultValue?: any) => any
  localeOptions: (options: any[]) => any[]
  localeProps: (props: any) => any
  makeOptionsRule: (options: any[]) => any
  makeRequiredRule: () => any
  makeTreeOptions: (options: any[]) => any[]
  makeTreeOptionsRule: (options: any[]) => any
  toJSON: (obj: any) => string
}

export interface FormDesignerInstance {
  install: (app: App) => void
  makeOptionsRule: (options: any[]) => any
  copyTextToClipboard: (text: string) => Promise<void>
  getInjectArg: (inject: any, name: string, defaultValue?: any) => any
  localeOptions: (options: any[]) => any[]
  localeProps: (props: any) => any
  makeRequiredRule: () => any
  makeTreeOptions: (options: any[]) => any[]
  makeTreeOptionsRule: (options: any[]) => any
  toJSON: (obj: any) => string
  formCreate: any
  designerForm: any
  component: (id: string, component: Component, previewComponent?: Component) => void
  useLocale: (locale: string) => void
  t: (key: string, ...args: any[]) => string
  utils: FormDesignerUtils
  version: string
}

declare const FcDesigner: FormDesignerInstance

export default FcDesigner

export {
  formCreate,
  designerForm,
  install,
  copyTextToClipboard,
  getInjectArg,
  localeOptions,
  localeProps,
  makeOptionsRule,
  makeRequiredRule,
  makeTreeOptions,
  makeTreeOptionsRule,
  toJSON
}

declare const formCreate: any
declare const designerForm: any
declare const install: (app: App) => void
declare const copyTextToClipboard: (text: string) => Promise<void>
declare const getInjectArg: (inject: any, name: string, defaultValue?: any) => any
declare const localeOptions: (options: any[]) => any[]
declare const localeProps: (props: any) => any
declare const makeOptionsRule: (options: any[]) => any
declare const makeRequiredRule: () => any
declare const makeTreeOptions: (options: any[]) => any[]
declare const makeTreeOptionsRule: (options: any[]) => any
declare const toJSON: (obj: any) => string

// Vue 组件类型声明
declare module '@vue/runtime-core' {
  interface GlobalComponents {
    FcDesigner: typeof FcDesigner
  }
}
