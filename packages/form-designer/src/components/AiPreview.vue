<template>
  <el-container class="_fc-json-preview">
    <BubbleList :list="list" max-height="100%" />
    <MentionSender v-model="query" variant="updown" clearable allow-speech @submit="send" />
  </el-container>
</template>

<script setup>
import { mastraClient } from '../utils/mastra.js'
import { ref } from 'vue'

const list = generateFakeItems(5)
const query = ref('')
function generateFakeItems(count) {
  const messages = []
  for (let i = 0; i < count; i++) {
    const role = i % 2 === 0 ? 'ai' : 'user'
    const placement = role === 'ai' ? 'start' : 'end'
    const key = i + 1
    const content =
      role === 'ai'
        ? '💖 感谢使用 Element Plus X ! 你的支持，是我们开源的最强动力 ~'.repeat(5)
        : `哈哈哈，让我试试`
    const loading = false
    const shape = 'corner'
    const variant = role === 'ai' ? 'filled' : 'outlined'
    const isMarkdown = false
    const typing = role === 'ai' ? i === count - 1 : false
    const avatar =
      role === 'ai'
        ? 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
        : 'https://avatars.githubusercontent.com/u/76239030?v=4'

    messages.push({
      key, // 唯一标识
      role, // user | ai 自行更据模型定义
      placement, // start | end 气泡位置
      content, // 消息内容 流式接受的时候，只需要改这个值即可
      loading, // 当前气泡的加载状态
      shape, // 气泡的形状
      variant, // 气泡的样式
      isMarkdown, // 是否渲染为 markdown
      typing, // 是否开启打字器效果 该属性不会和流式接受冲突
      isFog: role === 'ai', // 是否开启打字雾化效果，该效果 v1.1.6 新增，且在 typing 为 true 时生效，该效果会覆盖 typing 的 suffix 属性
      avatar,
      avatarSize: '24px', // 头像占位大小
      avatarGap: '12px', // 头像与气泡之间的距离
    })
  }
  return messages
}

async function send() {
  try {
    if(!query.value){
      return
    }
    const workflow = mastraClient.getWorkflow('testWorkflow')

    const run = await workflow.createRun()

    workflow.watch({ runId: run.runId }, (record) => {
      console.log(record)
    })

    const result = await workflow.start({
      runId: run.runId,
      inputData: {
        city: 'New York',
      },
    })
    console.log(result)
  } catch (e) {
    console.error(e)
  }
}
</script>

<style>
._fc-json-preview {
  display: flex;
  width: 100%;
  color: #262626;
}

._fc-json-preview {
  height: 100%;
  /* font-size: 12px; */
  padding: 12px;
  display: flex;
  flex-direction: column;
}
</style>
