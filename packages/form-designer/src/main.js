import { createApp } from 'vue'
import ELEMENT from 'element-plus'
import 'element-plus/dist/index.css'
import formCreate from '@form-create/element-ui'
import App from './App.vue'
import FcDesigner from './index'
import ElementPlusX from 'vue-element-plus-x'
const app = createApp(App)
// console.log('FcDesigner',FcDesigner)

app.use(ElementPlusX)
app.use(ELEMENT)
app.use(formCreate)
app.use(FcDesigner)

app.mount('#app')
